<mat-toolbar color="primary">
  Admin Dashboard
</mat-toolbar>

<div class="dashboard-content">

  <mat-card>
    <h3>Assign New Task</h3>
    <form>
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Worker Email</mat-label>
        <input matInput placeholder="Enter email" [(ngModel)]="workerEmail" name="workerEmail">
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Task Description</mat-label>
        <textarea matInput placeholder="Enter task details" [(ngModel)]="taskText" name="taskText"></textarea>
      </mat-form-field>

      <button mat-raised-button color="accent" (click)="assignTask()">
        Assign Task
      </button>
    </form>
  </mat-card>

  <mat-card class="mt">
    <h3>Assigned Tasks</h3>
    <mat-list>
      <mat-list-item *ngFor="let task of assignedTasks">
        {{ task.email }} — {{ task.task }} — Status: {{ task.status }}
      </mat-list-item>
    </mat-list>
  </mat-card>

</div>
