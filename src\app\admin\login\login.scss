.login-wrapper {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

.login-box {
  width: 100%;
  max-width: 450px;
  border-radius: 20px;
  background: white;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: slideIn 0.5s ease-out;
}

.full-width {
  width: 100%;
  margin-bottom: 20px;
}

.title {
  text-align: center;
  color: #333;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

mat-card-subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
}

mat-card-content {
  padding: 30px;
}

.login-btn {
  width: 100%;
  padding: 15px;
  font-weight: bold;
  font-size: 16px;
  margin-top: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.remember-me {
  margin: 20px 0;
  color: #555;
}

.login-links {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
  flex-wrap: wrap;
  gap: 10px;

  a {
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

// Form field styling
mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 8px;
  }

  .mat-mdc-form-field-error {
    color: #f44336;
    font-size: 12px;
  }
}

// Animation
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-wrapper {
    padding: 10px;
  }

  .login-box {
    max-width: 100%;
  }

  mat-card-content {
    padding: 20px;
  }

  .title {
    font-size: 24px;
  }

  .login-links {
    flex-direction: column;
    align-items: center;

    a {
      margin: 5px 0;
    }
  }
}
