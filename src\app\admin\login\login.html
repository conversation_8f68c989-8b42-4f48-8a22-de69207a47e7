<div class="login-wrapper">
  <mat-card class="login-box">
    <mat-card-header>
      <mat-card-title class="title">🚀 Task Tracker Login</mat-card-title>
      <mat-card-subtitle>Please enter your credentials to continue</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form (ngSubmit)="login()" #loginForm="ngForm">
        <!-- Role Selection -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Role</mat-label>
          <mat-select [(ngModel)]="role" name="role" required>
            <mat-option value="">Choose your role</mat-option>
            <mat-option value="admin">👨‍💼 Admin</mat-option>
            <mat-option value="worker">👷‍♂️ Worker</mat-option>
            <mat-option value="manager">👨‍💻 Manager</mat-option>
          </mat-select>
          <mat-error *ngIf="role === ''">Role is required</mat-error>
        </mat-form-field>

        <!-- Email Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email Address</mat-label>
          <input matInput [(ngModel)]="email" name="email" type="email" required
                 placeholder="Enter your email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="email === ''">Email is required</mat-error>
        </mat-form-field>

        <!-- Password Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input matInput [(ngModel)]="password" name="password"
                 [type]="hidePassword ? 'password' : 'text'" required
                 placeholder="Enter your password">
          <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword"
                  type="button" [attr.aria-label]="'Hide password'"
                  [attr.aria-pressed]="hidePassword">
            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="password === ''">Password is required</mat-error>
        </mat-form-field>

        <!-- Remember Me Checkbox -->
        <mat-checkbox [(ngModel)]="rememberMe" name="rememberMe" class="remember-me">
          Remember me
        </mat-checkbox>

        <!-- Login Button -->
        <button mat-flat-button color="primary" class="login-btn full-width"
                type="submit" [disabled]="!loginForm.form.valid">
          <mat-icon>login</mat-icon>
          🔐 Login
        </button>

        <!-- Additional Links -->
        <div class="login-links">
          <a mat-button color="accent" (click)="forgotPassword()">
            Forgot Password?
          </a>
          <a mat-button color="primary" (click)="createAccount()">
            Create New Account
          </a>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
