<div class="login-wrapper">
  <mat-card class="login-box">
    <h2 class="title">🚀 Task Tracker Login</h2>

    <form (ngSubmit)="login()">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Select Role</mat-label>
        <mat-select [(ngModel)]="role" name="role" required>
          <mat-option value="admin">Admin</mat-option>
          <mat-option value="worker">Worker</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Email</mat-label>
        <input matInput [(ngModel)]="email" name="email" type="email" required>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Password</mat-label>
        <input matInput [(ngModel)]="password" name="password" type="password" required>
      </mat-form-field>

      <button mat-flat-button color="primary" class="login-btn" type="submit">
        🔐 Login
      </button>
    </form>
  </mat-card>
</div>
