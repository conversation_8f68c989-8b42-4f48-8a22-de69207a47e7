<mat-toolbar color="primary">Worker Registration</mat-toolbar>

<div class="form-container">
  <form #registerForm="ngForm" (ngSubmit)="registerWorker()">

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Full Name</mat-label>
      <input matInput name="name" [(ngModel)]="worker.name" required>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Mobile Number</mat-label>
      <input matInput name="mobile" type="tel" [(ngModel)]="worker.mobile" required pattern="^[0-9]{10}$">
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Aadhaar Card Number</mat-label>
      <input matInput name="aadhaar" [(ngModel)]="worker.aadhaar" required pattern="^[0-9]{12}$">
    </mat-form-field>

    <button mat-raised-button color="accent" type="submit">Register</button>

  </form>
</div>
