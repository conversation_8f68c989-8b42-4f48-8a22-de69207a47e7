import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    MatIconModule,
    MatCheckboxModule
  ],
  templateUrl: './login.html',
  styleUrls: ['./login.scss']
})
export class LoginComponent {
  // Form properties
  email = '';
  password = '';
  role = '';
  rememberMe = false;
  hidePassword = true;

  // Login method
  login() {
    if (this.email === '<EMAIL>' && this.password === 'S@123456') {
      // Store remember me preference if needed
      if (this.rememberMe) {
        localStorage.setItem('rememberMe', 'true');
        localStorage.setItem('userEmail', this.email);
      }

      // Navigate based on role
      window.location.href = this.role === 'admin' ? '/admin' : '/worker';
    } else {
      alert("Invalid Credentials! Please check your email and password.");
    }
  }

  // Forgot password method
  forgotPassword() {
    alert("Forgot password functionality will be implemented soon!");
    // Here you can implement forgot password logic
    // For example: navigate to forgot password page or show modal
  }

  // Create account method
  createAccount() {
    alert("Account creation functionality will be implemented soon!");
    // Here you can implement account creation logic
    // For example: navigate to registration page
  }

  // Toggle password visibility
  togglePasswordVisibility() {
    this.hidePassword = !this.hidePassword;
  }

  // Initialize component
  ngOnInit() {
    // Check if user wants to be remembered
    const rememberMe = localStorage.getItem('rememberMe');
    if (rememberMe === 'true') {
      this.rememberMe = true;
      this.email = localStorage.getItem('userEmail') || '';
    }
  }
}
