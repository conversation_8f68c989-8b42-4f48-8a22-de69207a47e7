import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule
  ],
  templateUrl: './login.html',
  styleUrls: ['./login.scss']
})
export class LoginComponent {
  email = '';
  password = '';
  role = '';

  login() {
    if (this.email === '<EMAIL>' && this.password === 'S@123456') {
      window.location.href = this.role === 'admin' ? '/admin' : '/worker';
    } else {
      alert("Invalid Credentials");
    }
  }
  //   if (this.email === '<EMAIL>' && this.password === 'S@123456') {
  //     window.location.href = this.role === 'admin' ? '/admin' : '/worker';
  //   } else {
  //     alert("Invalid credentials");
  //   }

  // }
}
